<?php

namespace App\Filament\Resources\AgentResource\Pages;

use App\Enums\AgentModelType;
use App\Enums\AgentReasoningEffort;
use App\Enums\AgentVerbosity;
use App\Enums\CollectionType;
use App\Enums\SourceType;
use App\Features\AgentMedia;
use App\Models\Agent;
use App\Models\AgentModel;
use App\Models\Category;
use App\Models\Collection;
use App\Models\Contributor;
use App\Models\Model;
use App\Models\Source;
use App\Models\Tag;
use App\Models\Team;
use App\Services\Gatekeeper;
use App\Services\Labeller;
use Awcodes\Shout\Components\Shout;
use Closure;
use dacoto\DomainValidator\Validator\Domain;
use Filament\Forms;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieTagsInput;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Support\Enums\IconPosition;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use RalphJSmit\Filament\RecordFinder\Forms\Components\RecordFinder;

class EditRecord extends EditRecordTemplate
{

    protected static ?string $navigationLabel = 'Configuration';

    public function form(Form $form): Form
    {

        $canAdminister = Gatekeeper::userCanAdminister(Agent::class);
        $restrictEditingClosure = fn ($record) => restrict_editing($record);
        $restrictEditingOrAdminClosure = fn ($record) => restrict_editing($record) || ! $canAdminister;
        $cannotActivateAgentClosure = fn ($record) => !is_null($record) && !$record->team->credits()->canSpend($record->getCreditUsage());
        $team = get_current_team();

        $upgradeNotice = Shout::make('agent_upgrade_notice')
            ->content(fn ($record) => new HtmlString(view(
                'components.notices.agent-upgrade',
                [
                    'change_subscription_url' => route('filament.app.pages.billing.subscription', ['tenant'=>get_current_team_id()]),
                ]
            )))
            ->color('warning')
            ->icon(false)
            ->columnSpan(2)
        ;

        return $form
            ->schema([

                Shout::make('agent_disabled_notice')
                    ->visible($cannotActivateAgentClosure)
                    ->content(fn ($record) => new HtmlString(view(
                        'components.notices.agent-deactivated',
                        [
                            'buy_credits_url' => route('filament.app.pages.billing.products', ['tenant'=>get_current_team_id()]),
                            'edit_team_url' => route('filament.app.resources.teams.edit', ['tenant'=>get_current_team_id(), 'record'=>get_current_team_id()]),
                        ]
                    )))
                    ->color('warning')
                    ->icon(false)
                    ->columnSpan(2)
                ,

                Tabs::make('Tabs')
                    ->tabs([

                        static::getSummaryTab($cannotActivateAgentClosure)
                            ->disabled($restrictEditingClosure),

                        static::getInstructionsTab($team->getPlanOption('agent.instructions'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getSourcesTab(
                            $form,
                            $team->getPlanOption('agent.team_corpus'),
                            $team->getPlanOption('agent.custom_sources'),
                            $team->getPlanOption('agent.media'),
                            $upgradeNotice
                        )
                            ->disabled($restrictEditingClosure),

                        static::getModelTab(
                            $team->getPlanOption('agent.model_categories', true),
                            $team->getPlanOption('agent.instructions'),
                            $team->getPlanOption('agent.advanced_config'),
                            $upgradeNotice
                        )
                            ->disabled($restrictEditingClosure),

                        static::getTranslationsTab($team->getPlanOption('agent.realtime_translation'), $upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getApiTab($upgradeNotice)
                            ->disabled($restrictEditingClosure),

                        static::getListingTab()
                            ->disabled($restrictEditingClosure),

                        static::getAdminTab()
                            ->disabled($restrictEditingOrAdminClosure)
                            ->visible($canAdminister),

                    ])
                    ->persistTabInQueryString()
                    ->id('agent'),

            ])
            ->columns(1);
    }


    protected static function getSummaryTab(Closure $cannotActivateAgentClosure): Tabs\Tab
    {
        return Tabs\Tab::make('Summary')
            ->schema([

                static::getFormFieldName(true, false)
                    ->live(onBlur: true)
                    ->afterStateUpdated(function (Get $get, Set $set, $state) {
                        if (empty($get('slug'))) {
                            $set('slug', Str::slug($state));
                        }
                    })
                ,

                static::getFormFieldActive('
                    Only active Agents will be visible to the public.
                    This Agent will be disabled if you enable the "pay-as-you-go" team option and run out of credits.
                ')
                    ->disabled($cannotActivateAgentClosure)
                ,

                Fieldset::make('domain')
                    ->id('domain')
                    ->schema([
                        static::getFormFieldSlug()
                            ->label(false)
                            ->required(false)
                            ->requiredWithout('vanity_domain')
                            ->label('Subdomain')
                            ->columnSpan(['default' => 3]),
                        Select::make('root_domain')
                            ->label('Root Domain')
                            ->options(function () {
                                $options = [];
                                $domains = env_array('AGENT_ROOT_DOMAINS');
                                if (Gatekeeper::isAdmin()) {
                                    $domains = array_merge(env_array('AGENT_ADMIN_ROOT_DOMAINS'), $domains);
                                }
                                foreach ($domains as $rootDomain) {
                                    $options[$rootDomain] = ".{$rootDomain}";
                                }
                                return $options;
                            })
                            ->selectablePlaceholder(false)
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Agent will be available at [subdomain].[root_domain]. Note that each of the 2 parts are independently editable.',
                            )
                            ->columnSpan(['default' => 4]),
                    ])
                    ->label('Standard Subdomain')
                    ->columns(['default' => 7, 'xs' => 7, 'sm' => 7, 'md' => 7, 'lg' => 7, 'xl' => 7, '2xl' => 7])
                    ->columnSpan(1)
                ,

                TextInput::make('vanity_domain')
                    ->label('Vanity Domain')
                    ->maxLength(250)
                    ->rules([new Domain])
                    ->requiredWithout('slug')
                    ->unique(ignoreRecord: true, modifyRuleUsing: static::getUniqueByTeamAndTypeValidationClosure())
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'Agent will also be available at the exact domain that you enter, as long as you also activate it using the directions below.'
                    )
                    ->helperText(view('components.help.agent-vanity-domain'))
                ,

                static::getFormFieldActive(
                    "Allow users to view this Agent's Instructions and Sources for transparency. ",
                    'is_open_source',
                    'This Agent is Open Source'
                ),

            ])
            ->columns(2)
        ;
    }

    protected static function getInstructionsTab(bool $canSetInstructions, Shout $upgradeNotice): Tabs\Tab
    {
        $instructionsUpgradeNotice = clone $upgradeNotice;
        return Tabs\Tab::make('Instructions')
            ->schema([

                $instructionsUpgradeNotice
                    ->visible(!$canSetInstructions)
                ,

                Fieldset::make('Instructions')
                    ->schema([
                        Shout::make('instructions_notice')
                            ->content('These are the Agent\'s baseline operating instructions on what persona to take on and how to respond to prompts.')
                            ->color('primary')
                            ->icon('heroicon-s-information-circle')
                            ->columnSpan(2)
                        ,
                        Textarea::make('model_system_prompt')
                            ->label(false)
                            ->default(env('AGENT_DEFAULT_PROMPT'))
                            ->rows(20)
                            ->columnSpan(2)
                            ->disabled(!$canSetInstructions)
                        ,
                    ])
                ,

                Fieldset::make('Context')
                    ->schema([
                        Shout::make('context_instructions_notice')
                            ->content('These are the Agent\'s instructions on how to incorporate context into its response using merge fields.')
                            ->color('primary')
                            ->icon('heroicon-s-information-circle')
                            ->columnSpan(2)
                        ,

                        Textarea::make('model_context_prompt')
                            ->label(false)
                            ->default(env('AGENT_CONTEXT_PROMPT'))
                            ->helperText(view('components.help.agent-context-prompt'))
                            ->maxLength(1000)
                            ->rows(10)
                            ->columnSpan(2)
                            ->disabled(!$canSetInstructions)
                        ,
                    ])
                ,

                Fieldset::make('Agent Hierarchy')
                    ->schema([
                        Forms\Components\Select::make('parent')
                            ->label('Parent Agent')
                            ->relationship(
                                name: 'parent',
                                titleAttribute: 'name',
                                modifyQueryUsing: function (Builder $query, ?Agent $record) {
                                    $query
                                        ->approved()
                                        ->where(function (Builder $qry) {
                                            return $qry->where('is_extensible', true)
                                                ->orWhere('is_template', true);
                                        });
                                    if ($record) {
                                        $query->where('id', '!=', $record->id);
                                    }
                                }
                            )
                            ->searchable()
                            ->helperText("Instructions from the selected parent Agent will automatically be prepended to this Agent's instructions.")
                            ->disabled(!$canSetInstructions)
                        ,
                        static::getFormFieldActive(
                            new HtmlString("
                                Allow 3rd party Agents to prepend this Agent's instructions.
                                Note that the Agent's instructions will not be revealed to 3rd parties unless the 'This Agent is Open Source' option is enabled."
                            ),
                            'is_extensible',
                            'Allow Agent to be Extended'
                        )
                            ->disabled(!$canSetInstructions)
                        ,
                    ])
                ,

            ])
            ->columns(2)
        ;
    }

    protected static function getTranslationsTab(bool $canEnableRtt, Shout $upgradeNotice): Tabs\Tab
    {
        $translationUpgradeNotice = clone $upgradeNotice;
        $rttCredits = env_int('REALTIME_TRANSLATION_CREDITS');
        return Tabs\Tab::make('Translations')
            ->schema([

                Fieldset::make('Languages')
                    ->schema([
                        Select::make('supported_languages')
                            ->label('Supported Languages (leave blank for all)')
                            ->options(fn (Get $get, ?\Illuminate\Database\Eloquent\Model $record) => Agent::getAvailableLanguages(
                                $get('auto_translate'),
                                $get('model_id'),
                                $record
                            ))
                            ->multiple()
                            ->live()
                        ,
                        Select::make('default_language')
                            ->label('Default Language')
                            ->options(fn (Get $get, ?Model $record) => Agent::getSupportedLanguages(
                                $get('supported_languages'),
                                $get('auto_translate'),
                                $get('model_id'),
                                $record
                            ))
                            ->default(env('AGENT_DEFAULT_LANGUAGE'))
                            ->selectablePlaceholder(false)
                        ,
                        static::getFormFieldActive(
                            new HtmlString("
                                <strong>Please note:</strong> Enabling this option will use an additional <strong>{$rttCredits} credits</strong>
                                per response for selected languages, or for <strong>all non-English languages</strong> if none are selected.
                            "),
                            'auto_translate',
                            'Use Real Time Translation for Specified Languages'
                        )
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Expand language support to 192 languages by using a translation service to translate user prompts to English, and Agent responses back into the original prompt language.',
                            )
                            ->live()
                            ->disabled(!$canEnableRtt)
                        ,
                        Select::make('auto_translate_languages')
                            ->label('Real Time Translation Languages (leave blank for all)')
                            ->options(fn (Get $get, ?Model $record) => Agent::getRealTimeTranslationLanguages(
                                $get('supported_languages'),
                                [env('TRANSLATION_DEFAULT_LANGUAGE')]
                            ))
                            ->multiple()
                            ->disabled(fn (Get $get) => !$get('auto_translate'))
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Only the languages selected here will use real time translation. English will never use it.',
                            )
                            ->live()
                        ,
                    ])
                ,

                Fieldset::make('Bible Translations')
                    ->schema([
                        Select::make('supported_translations')
                            ->label('Supported Translations (leave blank for all)')
                            ->options(config('agent.translations'))
                            ->multiple()
                            ->live()
                        ,
                        Select::make('default_translation')
                            ->label('Default Translation')
                            ->options(fn (Get $get, ?Model $record) => Agent::getSupportedTranslations($get('supported_translations'), $record))
                            ->default(env('AGENT_DEFAULT_TRANSLATION'))
                            ->selectablePlaceholder(false)
                        ,
                    ])
                ,

            ])
            ->columns(2)
            ;
    }

    protected static function getSourcesTab(Form $form, bool $canEnableTeamCorpus, bool $canSelectCustomSources, bool $canEnableMedia, Shout $upgradeNotice): Tabs\Tab
    {

        $teamCorpusUpgradeNotice = clone $upgradeNotice;
        $customSourcesUpgradeNotice = clone $upgradeNotice;
        $hasCustomCorpus = get_current_team()->has_custom_corpus;

        return Tabs\Tab::make('Knowledge')
            ->schema([

                $teamCorpusUpgradeNotice
                    ->visible(!$canEnableTeamCorpus)
                ,

                Shout::make('sources_notice')
                    ->visible(!$hasCustomCorpus)
                    ->content(new HtmlString('
                        All approved content contributed by the community will be used if no constraints are added here.
                        Every effort is made to validate the authenticity and relevance of materials provided by 3rd party contributors.
                        We are not responsible for copyright infringement on the part of content contributors.
                    '))
                    ->color('primary')
                    ->icon('heroicon-s-information-circle')
                    ->columnSpan(2)
                ,

                Shout::make('custom_corpus_notice')
                    ->visible($hasCustomCorpus)
                    ->content(new HtmlString('
                        Your team is configured to use a custom corpus.
                        This Agent will therefore not have access to community sources.
                        You may only use sources that you upload to this team.
                        Contact us if you would like to revert back to using the community corpus.
                    '))
                    ->color('warning')
                    ->icon('heroicon-s-exclamation-triangle')
                    ->columnSpan(2)
                ,

                static::getFormFieldActive(
                    'Use all Sources contributed by your team, regardless of whether they are in the community corpus or not.',
                    'use_team_corpus',
                    'Use All Team Sources'
                )
                    ->default(true)
                    ->disabled(!$canEnableTeamCorpus || $hasCustomCorpus)
                ,

                static::getFormFieldActive(
                    'Do not use any community Sources as context, other than what is specifically selected below.',
                    'disable_community_corpus',
                    'Disable Community Sources'
                )
                    ->disabled(!$canEnableTeamCorpus || $hasCustomCorpus)
                ,

                Forms\Components\Fieldset::make('sources')
                    ->label('Text Sources to Reference')
                    ->schema([
                        $customSourcesUpgradeNotice
                            ->visible($canEnableTeamCorpus && !$canSelectCustomSources)
                        ,
                        static::getFormFieldCategoryFinder()
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldCollectionFinder($hasCustomCorpus)
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldContributorFinder()
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldSourceFinder($hasCustomCorpus)
                            ->disabled(!$canSelectCustomSources)
                        ,
                        static::getFormFieldClassification()
                            ->hintIcon(
                                'heroicon-s-question-mark-circle',
                                tooltip: 'Only sources that match this denominational alignment will be included.',
                            )
                            ->disabled(!$canEnableTeamCorpus)
                        ,
                        SpatieTagsInput::make('tags')
                            ->reorderable()
                            ->suggestions(fn () => Tag::query()->team()->pluck('name')->all())
                        ,
                    ])
                ,

            ])
            ->columns(2)
        ;

    }

    protected static function getModelTab(array $modelTypes, bool $canSetInstructions, bool $canSetAdvancedConfig, Shout $upgradeNotice): Tabs\Tab
    {

        if (Gatekeeper::isAdmin()) {
            $modelTypes = AgentModelType::values();
        }
        $modelOptions = [];
        foreach ($modelTypes as $type)
        {
            $modelOptions[Str::title($type)] = [];
            foreach (AgentModel::where('type', $type)->where('is_active', true)->orderBy('seq', 'ASC')->get() as $model)
            {
                $modelOptions[Str::title($type)][$model->id] = "{$model->name} ({$model->num_credits})";
            }
        }

        $advancedConfigUpgradeNotice = clone $upgradeNotice;

        return Tabs\Tab::make('Model')
            ->schema([

                Shout::make('advanced_notice')
                    ->content('The options below can drastically change how the Agent responds. Make sure to thoroughly test changes to these parameters.')
                    ->color('warning')
                    ->icon('heroicon-o-exclamation-triangle')
                    ->columnSpan(2)
                ,

                Select::make('model_id')
                    ->label('Model (# credits)')
                    ->options($modelOptions)
                    ->afterStateUpdated(fn (string $state, Set $set) => $set('model_max_tokens', AgentModel::find($state)->max_tokens))
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The underlying base model to use. The number of credits used per response is listed in parentheses after the model name.'
                    )
                    ->required()
                    ->default(env_int('AGENT_DEFAULT_MODEL_ID'))
                    ->live()
                    ->columnSpan(2)
                ,

                $advancedConfigUpgradeNotice
                    ->visible($canSetInstructions && !$canSetAdvancedConfig)
                ,
                Select::make('model_reasoning_effort')
                    ->label('Reasoning Effort')
                    ->options(AgentReasoningEffort::asOptions())
                    ->default(AgentReasoningEffort::MEDIUM)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'This option is only available on select reasoning models.'
                    )
                    ->disabled(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_reasoning_effort))
                    ->hidden(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_reasoning_effort))
                ,
                Select::make('model_verbosity')
                    ->label('Verbosity')
                    ->options(AgentVerbosity::asOptions())
                    ->default(AgentVerbosity::MEDIUM)
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'This option is only available on select models.'
                    )
                    ->disabled(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_verbosity))
                    ->hidden(fn (Get $get) => !empty($get('model_id') && !AgentModel::find($get('model_id'))->supports_verbosity))
                ,
                static::getFormFieldSlider(
                    'model_temperature',
                    'Temperature',
                    'The amount of creativity (variability) allowed. Values from from 0.0 to 2.0. Some reasoning models don\'t support this.',
                    0,
                    2,
                    .05
                )
                    ->default(env_float('AGENT_DEFAULT_TEMPERATURE'))
                    ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_temperature))
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_temperature))
                ,
                static::getFormFieldSlider(
                    'model_top_p',
                    'Top P',
                    'The probability cut-off of the token results to be considered. Lower values mean higher probability. Values from 0.0 to 1.0. Some reasoning models don\'t support this.',
                    0,
                    1,
                    .05
                )
                    ->default(env_float('AGENT_DEFAULT_TOPP'))
                    ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_top_p))
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_top_p))
                ,
                static::getFormFieldSlider(
                    'model_frequency_penalty',
                    'Frequency Penalty',
                    'How much to penalize new tokens based on their existing frequency in the text so far, to reduce repetition. Values from -2.0 to 2.0. Some reasoning models don\'t support this.',
                    -2,
                    2,
                    .1
                )
                    ->default(0)
                    ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_frequency_penalty))
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_frequency_penalty))
                ,
                static::getFormFieldSlider(
                    'model_presence_penalty',
                    'Presence Penalty',
                    'How much to penalize new tokens based on whether they appear in the text so far, to increase the likelihood of new topics being mentioned. Values from -2.0 to 2.0. Some reasoning models don\'t support this.',
                    -2,
                    2,
                    .1
                )
                    ->default(0)
                    ->disabled(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_presence_penalty))
                    ->hidden(fn (Get $get) => !$canSetAdvancedConfig || (!empty($get('model_id')) && !AgentModel::find($get('model_id'))->supports_presence_penalty))
                ,
                static::getFormFieldSlider(
                    'model_max_tokens',
                    'Max Output Tokens',
                    'The maximum number of response tokens.',
                    1024,
                    fn (Get $get) => $get('model_id') ?
                        AgentModel::find($get('model_id'))->max_tokens :
                        env_int('AGENT_DEFAULT_MAX_TOKENS')
                    ,
                    1024
                )
                    ->default(1024)
                    ->disabled(!$canSetAdvancedConfig)
                ,

            ])
            ->columns(2)
        ;
    }

    protected static function getApiTab(Shout $upgradeNotice): Tabs\Tab
    {

        $formats = [
            'raw' => [
                'label' => 'OpenAI Compatible Stream',
                'description' => 'Best for streaming use cases as a direct swap-in to replace OpenAI integrations. Use any OpenAI SDK.',
                'icon' => 'heroicon-s-cube-transparent',
            ],
            'text' => [
                'label' => 'Plain Text / Markdown',
                'description' => 'Best for use cases where the raw text is desired in order to process it or display it in a non-web context.',
                'icon' => 'heroicon-s-italic',
            ],
            'html' => [
                'label' => 'HTML',
                'description' => 'Best for directly displaying output on a web page. Any markdown output is automatically converted to HTML.',
                'icon' => 'heroicon-s-code-bracket',
            ],
            'json' => [
                'label' => 'JSON',
                'description' => 'Best for non-streaming use cases or where a structured response is required, such as platform integrations.',
                'icon' => 'heroicon-o-square-3-stack-3d',
            ],
        ];

        return Tabs\Tab::make('API')
            ->schema([

                static::getFormFieldCardSelector('api_response_format', 'Default Response Format', $formats)
                    ->default('raw')
                ,

                TextInput::make('max_memories')
                    ->label('Max Context Memories')
                    ->numeric()
                    ->step(1)
                    ->minValue(0)
                    ->maxValue(10)
                    ->default(env('AGENT_DEFAULT_MEMORIES'))
                    ->hintIcon(
                        'heroicon-s-question-mark-circle',
                        tooltip: 'The maximum number of previous prompt/response exchanges to provide the model as context. Values 0 to 10.'
                    )
                ,

                static::getFormFieldActive(
                    '
                        Remove Markdown characters from plain text responses.
                        Useful for voice integrations or elsewhere that Markdown won\'t work well.
                    ',
                    'strip_markdown',
                    'Strip Markdown from Response Text'
                ),

                static::getFormFieldActive(
                    '
                        Prevent API requests from overriding this Agent\'s system prompt.
                        Useful for integrations that require an undesired system prompt override.
                    ',
                    'lock_system_prompt',
                    'Lock System Prompt'
                ),

            ])
            ->columns(2)
        ;
    }

    protected static function getListingTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Listing')
            ->icon('heroicon-s-language')
            ->iconPosition(IconPosition::Before)
            ->schema([
                static::getTranslateActions('listing', ['persona_name', 'persona_tagline', 'persona_description', 'target_worldview'])
                    ->columnSpan(2)
                ,
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldActive(
                            'Advertise this Agent publicly on Apologist Agent Marketplace. You must submit your Agent for approval in order for it to be listed on the Marketplace.',
                            'marketplace_active',
                            new HtmlString("List on <a href='' target='_blank' class='hover:underline text-gray-900 dark:text-white font-bold'>Apologist Agent Marketplace ↗</a>.")
                        ),
                        TextInput::make('persona_name')
                            ->label('Name'),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                Forms\Components\Group::make()
                    ->schema([
                        static::getFormFieldAvatar('agents/personas', 'Avatar', 'persona_avatar_path'),
                    ])
                    ->columnSpan(['default' => 2, 'md' => 1]),
                TextInput::make('persona_tagline')
                    ->label('Tagline')
                ,
                TextInput::make('target_worldview')
                    ->label('Target Worldview')
                    ->maxLength(100)
                ,
                static::getFormFieldBlockRichText('persona_description')
                    ->label('Description'),
            ])
            ->columns(2);
    }

    protected static function getAdminTab(): Tabs\Tab
    {
        return Tabs\Tab::make('Admin')
            ->schema([
                static::getFormFieldActive(
                    'Show this Agent in the worldview selector.',
                    'is_selectable',
                    'Selectable as Worldview'
                ),
                static::getFormFieldActive(
                    'Allow this Agent to be selected as a 1st party base template.',
                    'is_template',
                    'Selectable as Base Template'
                ),
                static::getFormFieldTeam(),
                static::getFormFieldOwner(),
                static::getFormFieldActive(
                    'Enable semantic search via this Agent\'s API.',
                    'has_semantic_search',
                    'Enable Semantic Search'
                ),
                static::getFormFieldActive(
                    'Do not charge the team for use of this Agent.',
                    'is_nonbillable',
                    'Do Not Bill'
                )
                    ->default(fn (Get $get) => Team::find($get('team'))?->is_nonbillable),
                static::getFormFieldActive(
                    'Show debugging logs.',
                    'debug',
                    'Debug'
                ),
            ])
            ->columns(2)
            ;
    }

    protected static function getFormFieldSourceFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'sources',
            Source::class,
            Source::where('agent_active', true)->whereNotNull('agent_indexed_at'),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
                Tables\Columns\TextColumn::make('published_on')
                    ->label('Published')
                    ->date()
                    ->sortable(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(SourceType::asOptions()),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('collections')
                    ->getOptionLabelFromRecordUsing(function (Model $record) {
                        $type = Labeller::mutate($record->type->value);
                        return "{$record->name} [{$type}]";
                    }),
                static::getTableFilterContributors(),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Source $source) {
                $type = Labeller::mutate($source->type->value);
                return new HtmlString("<span class='font-bold'>{$source->name}</span> [{$type}]");
            },
            false,
            'Individual Sources',
            'Only the specific Sources selected will be included.'
        );
    }

    protected static function getFormFieldCollectionFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'collections',
            Collection::class,
            Collection::query(),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnType(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
                static::getTableFilterType(CollectionType::asOptions()),
                static::getTableFilterLanguage(),
                static::getTableFilterRelation('categories'),
                static::getTableFilterRelation('classification', 'Denominational Alignment'),
            ],
            function (Collection $collection) {
                $type = Labeller::mutate($collection->type->value);
                return new HtmlString("<span class='font-bold'>{$collection->name}</span> [{$type}]");
            },
        );
    }

    protected static function getFormFieldContributorFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'contributors',
            Contributor::class,
            Contributor::query(),
            fn () => [
                static::getTableColumnName(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
            ]
        );
    }

    protected static function getFormFieldCategoryFinder(): RecordFinder
    {
        return static::getFormFieldRecordFinder(
            'categories',
            Category::class,
            Category::active(),
            fn () => [
                static::getTableColumnName(),
                static::getTableColumnParent(),
            ],
            fn () => [
                static::getTableFilterMyTeam(),
            ],
            function (Category $category) {
                return new HtmlString(
                    "<span class='font-bold'>{$category->name}</span>".
                    ($category->parent ? " [{$category->parent->name}]" : '')
                );
            },
            true
        );
    }

    protected static function getFormActionsAgent(): Forms\Components\Actions
    {
        $shouldIndex = fn ($record) => ! is_null($record) && ! $record->is_locked && $record->shouldIndexRag();
        $sourceIdClosure = fn ($record) => $record->id;

        return Forms\Components\Actions::make([
            static::getAgentDemoAction(
                '
                    Launch an instance of Agent that draws only on context from this Source. This will give you a good
                    idea of what it will look like if/when Agent uses your Source in its response.
                ',
                $shouldIndex,
                $sourceIdClosure
            ),
            Forms\Components\Actions\Action::make('learn')
                ->extraAttributes(['id' => btn_id('learn')])
                ->label('Learn More ↗')
                ->icon('heroicon-s-book-open')
                ->color('gray')
                ->url(env('AGENT_LEARN_URL'))
                ->openUrlInNewTab(),
            Forms\Components\Actions\Action::make('reindex')
                ->extraAttributes(['id' => btn_id('reindex')])
                ->label('Reindex')
                ->tooltip('Manually reindex this source on Apologist Agent if it has become out of sync.')
                ->color('gray')
                ->outlined()
                ->icon('heroicon-s-circle-stack')
                ->visible($shouldIndex)
                ->action(function (?\App\Models\Model $record) {
                    dispatch(function () use ($record) {
                        $record->reindexRagDocument();
                    })->catch(function (Throwable $e) {
                        // This job has failed...
                    });
                    Notification::make()
                        ->title('Submitted for Reindexing')
                        ->success()
                        ->send();

                    return true;
                })
                ->requiresConfirmation()
                ->modalIcon('heroicon-s-circle-stack'),
        ])
            ->alignRight()
        ;
    }



}
